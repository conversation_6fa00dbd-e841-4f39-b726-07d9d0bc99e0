/*
Copyright © 2025 YOUR NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"os"

	"github.com/spf13/cobra"
)

// rootCmd 代表在不带任何子命令的情况下调用的基础命令。
var rootCmd = &cobra.Command{
	Use:   "toolbox",
	Short: "一个强大且智能的运维排障工具箱。",
	Long: `一个强大且智能的运维排障工具箱。

它集成了针对 Kubernetes、Linux、Docker 等多种环境的自动化诊断能力，
旨在帮助 SRE 和运维工程师快速定位并解决问题。`,
	// 如果您的应用程序有裸操作，请取消注释以下行：
	// Run: func(cmd *cobra.Command, args []string) { },
}

// Execute 将所有子命令添加到根命令中，并适当设置标志。
// 这是由 main.main() 调用的。它只需要对 rootCmd 调用一次。
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	// 在这里，您将定义标志和配置设置。
	// Cobra 支持持久性标志，如果在此处定义，则这些标志将是全局的。

	// rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件 (默认为 $HOME/.toolbox.yaml)")

	// Cobra 也支持本地标志，只有当直接调用此操作时才会运行。
	rootCmd.Flags().BoolP("toggle", "t", false, "切换帮助信息")
}


