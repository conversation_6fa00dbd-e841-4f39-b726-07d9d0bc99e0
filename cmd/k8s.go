/*
Copyright © 2025 YOUR NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"bytes"
	"fmt"
	"os/exec"
	"strings"
	"sync"

	"github.com/spf13/cobra"
)

// k8sCmd 代表 k8s 命令
var k8sCmd = &cobra.Command{
	Use:   "k8s",
	Short: "对 Kubernetes 集群进行全面诊断，并由 LLM 进行智能汇总。",
	Long: `对 Kubernetes 集群进行全面诊断。

该命令会并发执行一组预定义的、只读的 kubectl 命令，
以收集集群健康的全面快照。然后将原始数据发送给大语言模型（LLM）
进行智能分析和汇总，并最终展示出结构化的、易于理解的诊断报告。`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("阶段 1: 开始对 Kubernetes 集群进行自主诊断...")

		// "诊断套餐"
		diagnosticPackage := []string{
			"kubectl get nodes -o wide",
			"kubectl get pods -A --field-selector=status.phase!=Running,status.phase!=Succeeded",
			"kubectl get events -A --sort-by='.lastTimestamp' | tail -n 20",
			"kubectl top nodes",
			"kubectl top pods -A",
			"kubectl get componentstatuses",
			"kubectl get pvc -A --field-selector=status.phase!=Bound",
		}

		// 并发执行与结果收集
		var wg sync.WaitGroup
		resultsChan := make(chan string, len(diagnosticPackage))

		for _, command := range diagnosticPackage {
			wg.Add(1)
			go func(c string) {
				defer wg.Done()
				var out bytes.Buffer
				var stderr bytes.Buffer

				cmd := exec.Command("bash", "-c", c)
				cmd.Stdout = &out
				cmd.Stderr = &stderr

				cmd.Run() // 在这个阶段，我们暂时不处理错误，因为无论成功失败，我们都希望 LLM 能看到所有输出

				var resultBuilder strings.Builder
				resultBuilder.WriteString("---\n")
				resultBuilder.WriteString(fmt.Sprintf("命令: %s\n", c))
				if stderr.Len() > 0 {
					resultBuilder.WriteString(fmt.Sprintf("错误输出:\n%s\n", stderr.String()))
				}
				if out.Len() > 0 {
					resultBuilder.WriteString(fmt.Sprintf("标准输出:\n%s\n", out.String()))
				}
				resultsChan <- resultBuilder.String()
			}(command)
		}

		wg.Wait()
		close(resultsChan)

		var rawReportBuilder strings.Builder
		for result := range resultsChan {
			rawReportBuilder.WriteString(result)
		}
		rawReport := rawReportBuilder.String()

		fmt.Println("阶段 1 完成。原始诊断数据已收集。")
		// fmt.Println(rawReport) // 默认不打印原始报告，因为它可能非常长

		fmt.Println("\n阶段 2: 开始进行 LLM 智能汇总...")

		// 任务 2.1: 对接 LLM API
		client := newLLMClient()

		// 任务 2.2: 实现“智能汇总” Prompt
		prompt := "你是一位顶级的 SRE 专家。附件是 Kubernetes 集群的健康快照。请仔细分析所有信息，忽略所有正常的、健康的输出，帮我识别出当前最紧急、最需要关注的 1-3 个问题。以清晰的、分点的摘要形式返回，并为每个问题提供一个可执行的、用于深入排查的 CLI 命令。\n\n--- 健康快照 --- \n" + rawReport

		// 任务 2.3: 解析并展示结果
		summary, err := client.getCompletion(prompt)
		if err != nil {
			fmt.Printf("LLM 智能汇总失败: %v\n", err)
			return
		}

		fmt.Println("\n--- LLM 智能诊断报告 ---")
		fmt.Println(summary)
		fmt.Println("--- 报告结束 ---")
	},
}

func init() {
	diagnoseCmd.AddCommand(k8sCmd)
}