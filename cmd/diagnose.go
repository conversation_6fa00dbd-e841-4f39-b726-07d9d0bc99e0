/*
Copyright © 2025 YOUR NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

// diagnoseCmd 代表 diagnose 命令
var diagnoseCmd = &cobra.Command{
	Use:   "diagnose",
	Short: "执行特定目标的诊断任务。",
	Long: `执行特定目标的诊断任务。

使用此命令及其子命令（如 k8s, linux）来对特定系统进行健康检查和问题排查。`,
	Run: func(cmd *cobra.Command, args []string) {
		// 如果直接运行 diagnose，提示用户选择一个子命令
		fmt.Println("请指定一个诊断目标，例如: toolbox diagnose k8s")
	},
}

func init() {
	rootCmd.AddCommand(diagnoseCmd)

	// 在这里，您可以定义标志和配置设置。

	// Cobra 支持持久性标志，该标志可用于此命令及其所有子命令。
	// 例如:
	// diagnoseCmd.PersistentFlags().String("foo", "", "一个 foo 的帮助信息")

	// Cobra 支持本地标志，只有在直接调用此命令时才会运行。
	// 例如:
	// diagnoseCmd.Flags().BoolP("toggle", "t", false, "切换帮助信息")
}
