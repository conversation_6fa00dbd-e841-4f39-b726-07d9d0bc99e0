# LLM 驱动的 K8s/Linux/Nginx/Docker 智能排障工具箱 v3.0 (自主诊断版)

## 1. 项目愿景 (v3.0)

构建一个能**自主发现问题**的智能排障工具。用户只需指定诊断目标（如 K8s 集群），工具即可自动完成全面的健康检查，通过 LLM 提炼出核心问题摘要，并引导用户对具体问题进行深入的、交互式的诊断和修复。它将排查过程从“人问，机答”的被动模式，转变为“**机诊，人断**”的主动模式。

## 2. 核心架构 (v3.0): 三阶段工作流

该架构旨在最大限度地减少用户的认知负担，让工具完成大部分的初步分析工作。

### **阶段一: 自主诊断 (Autonomous Diagnosis)**

- **触发**: 用户发起一个高阶、模糊的诊断指令。例如: `./toolbox diagnose k8s`。
- **动作**: Go CLI 执行一个预设的“诊断套餐”。这是一个并发执行的、涵盖广度的、只读的命令集合，用于抓取目标的整体健康快照。
- **K8s 诊断套餐示例**:
    - `kubectl get nodes -o wide`
    - `kubectl get pods -A --field-selector=status.phase!=Running,status.phase!=Succeeded` (获取所有非正常状态的 Pod)
    - `kubectl get events -A --sort-by='.lastTimestamp' | tail -n 30` (获取最新的30条事件)
    - `kubectl top nodes`
    - `kubectl get componentstatuses`
    - `kubectl get pvc -A --field-selector=status.phase!=Bound` (获取未绑定的 PVC)
- **目标**: 无需用户干预，自动、全面地收集原始诊断数据。

### **阶段二: LLM 智能汇总 (LLM-Powered Summary)**

- **触发**: 阶段一的数据收集完成。
- **动作**: Go CLI 将所有收集到的原始文本（可能非常庞杂）整合，作为一个大的上下文，发送给 LLM。
- **关键 Prompt**: `"你是一位顶级的 SRE 专家。附件是 Kubernetes 集群的健康快照。请仔细分析所有信息，忽略所有正常的、健康的输出，帮我识别出当前最紧急、最需要关注的 1-3 个问题。以清晰的、分点的摘要形式返回，并为每个问题提供一个可执行的、用于深入排查的 CLI 命令。"`
- **LLM 输出 (示例)**:
    ```text
    **集群健康摘要:**

    *   **[严重] 节点 NotReady**: 节点 `worker-03` 处于 `NotReady` 状态，原因为 `MemoryPressure`。
    *   **[警告] 应用循环崩溃**: `prod` 命名空间下的 Pod `api-gateway-xyz` 正在 `CrashLoopBackOff`。
    *   **[信息] 核心组件异常**: `kube-system` 中的 `coredns-abc` Pod 处于 `Pending` 状态。

    **建议的下一步操作:**
    1. 调查节点问题: `./toolbox diagnose k8s-node worker-03`
    2. 调试崩溃的Pod: `./toolbox diagnose k8s-pod api-gateway-xyz -n prod`
    3. 检查 DNS Pod: `./toolbox diagnose k8s-pod coredns-abc -n kube-system`
    ```
- **目标**: 将海量原始数据降噪、提炼，直接向用户展示问题的核心，并提供清晰的行动指引。

### **阶段三: 交互式深潜 (Interactive Deep-Dive)**

- **触发**: 用户根据 LLM 的建议，选择一个具体问题，并执行其建议的命令。
- **动作**: 此时，系统进入 **v2.0 方案中描述的“智能诊断循环”**。
    - 工具会聚焦于这一个特定目标（如某个 Pod）。
    - 它会执行一个初步的诊断命令（如 `kubectl describe pod ...`）。
    - 然后将结果发给 LLM，并请求下一步指令（如 `kubectl logs --previous ...`）。
    - 这个循环会持续进行，直到该特定问题被定位或解决。
- **目标**: 对已知的、具体的问题进行集中的、专家式的、多轮的诊断。

## 3. 优势总结

- **高效**: 用户无需了解繁杂的排查命令，只需一步即可获得集群的“体检报告”。
- **智能**: 利用 LLM 强大的自然语言处理和推理能力，从噪音中发现信号。
- **专注**: 将广泛的“巡检”和深入的“诊断”分离，让用户可以专注于解决已经被识别出的问题。
- **用户友好**: 整个体验非常流畅，符合人类解决问题的思维习惯：先看全局，再钻细节。

## 4. 实施规划

1.  **搭建 Go CLI 框架** (使用 Cobra)。
2.  **实现阶段一**: 为 `diagnose k8s` 命令硬编码“诊断套餐”中的所有命令，并实现并发执行和结果收集。
3.  **实现阶段二**: 实现与 LLM 的 API 对接，构建并发送“智能汇总”的 Prompt，并对返回结果进行格式化展示。
4.  **实现阶段三**: 复用 v2.0 的设计，为 `diagnose k8s-pod` 等具体命令实现“交互式深潜”的诊断循环。
5.  **扩展**: 将此模式推广到 `diagnose linux`, `diagnose docker` 等其他模块。

## 5. 核心技术概念详解 (Technical Deep-Dive)

### 5.1. 数据收集：“诊断套餐”模式 (Data Collection: The "Diagnostic Package" Model)

此模式是**阶段一：自主诊断**的核心。我们不依赖 LLM 来决定初筛阶段需要哪些信息，而是由人类专家预先定义一套可靠、安全、高效的“诊断套餐”。

- **方案核心**:
    1.  **预定义命令集 (Hardcoded Commands)**: 在 Go CLI 工具中，为每种诊断目标（`k8s`, `linux` 等）硬编码一个精心挑选的命令列表。这些命令必须是**只读的、安全的**，确保不会对用户系统进行任何修改。
    2.  **并发执行 (Concurrent Execution)**: 工具并发执行套餐内的所有命令，以最快速度收集全面的系统健康快照。
    3.  **统一汇总 (Unified Aggregation)**: 程序捕获所有命令的标准输出 (`stdout`) 和标准错误 (`stderr`)，将它们整合成一个单一、巨大的文本块。
    4.  **一次性交付 (Single-Shot Delivery)**: 这个完整的“健康快照”将作为完整的上下文，一次性提交给 LLM 进行分析，确保 LLM 能进行最高质量的全局判断。

- **优势**: 此方案如同医院的“标准体检”，保证了初始数据收集的**可靠性、高效性和全面性**，为后续 LLM 的精准分析打下坚实基础。

### 5.2. 对话记忆与 Token 管理 (Conversation Memory & Token Management)

此部分是**阶段三：交互式深潜**的关键，用于解决 LLM 无状态（stateless）的问题。记忆的责任完全由 Go CLI 客户端承担。

- **方案核心**:
    1.  **客户端维护历史 (Client-Side History)**: Go 程序在内存中维护一个对话历史列表，记录从交互开始的每一轮“用户输入”（通常是命令输出）和“模型回答”（分析和建议）。
    2.  **上下文传递 (Context Passing)**: 在每一轮新的交互中，程序会将**完整的对话历史**连同最新的命令输出一起发送给 LLM。这使得 LLM 能够“记住”之前的上下文，并做出连贯的、有逻辑的推理。

- **Token 超限应对策略**:
    - **滑动窗口 (Sliding Window)**: 最简单直接的策略。只保留最初的提问和最近 N 轮的对话，丢弃中间部分。优点是实现简单，缺点是可能丢失重要历史信息。
    - **对话摘要 (Summarization)**: 更优的策略。当 Token 接近上限时，额外调用一次 LLM，将当前对话历史进行**精炼总结**。然后用这个高度浓缩的摘要替换掉多轮的详细对话，从而大幅减少 Token 占用。
    - **智能修剪 (Intelligent Pruning)**: 结合业务逻辑，由程序或 LLM 判断并丢弃那些已被确认为“正常”或“不相关”的诊断输出，只保留关键信息。
    - **向量数据库 (Vector DB / RAG)**: 最先进的方案。将所有对话历史存入向量数据库，每次请求时，根据当前问题**检索最相关的历史片段**作为上下文。此方案效果最好，但实现也最复杂。

- **选型建议**: 对于此工具，**“对话摘要”** 与 **“滑动窗口”** 相结合的策略，是在实现成本和效果之间的一个理想平衡点。

### 5.3. 工具调用与安全：LLM 作为“大脑”，CLI 作为“手脚” (Tool Use & Safety: LLM as the "Brain", CLI as the "Hands")

这是实现 Agent 行为的核心设计模式，确保 LLM 的智能可以被安全、可控地执行。

- **方案核心**:
    1.  **LLM 负责决策，不负责执行**: 严禁 LLM 直接调用任何系统命令。它的角色是分析信息，并决定**“下一步应该做什么”**。
    2.  **结构化指令输出 (Structured Output)**: 通过 Prompt Engineering，我们强制要求 LLM 以结构化格式（如 JSON）返回它的决策。这个 JSON 中清晰地定义了建议使用的工具（命令）和相关参数。
        - **示例 Prompt 指示**: `...请以 JSON 格式返回你的建议，格式为 {"tool_name": "command", "args": ["arg1", "arg2"]}`
        - **示例 LLM 输出**: `{"analysis": "Pod 日志显示 OOMKilled，需要检查资源限制。", "tool_name": "kubectl", "args": ["describe", "pod", "my-pod"]}`
    3.  **CLI 负责解析、验证和执行**:
        - **解析 (Parse)**: Go 程序接收并解析 LLM 返回的 JSON。
        - **验证 (Validate)**: 程序会维护一个**命令白名单 (Allowlist)**。只有当 `tool_name` 存在于这个安全的白名单中时，命令才被允许执行。这是防止 LLM “幻觉”出危险命令（如 `rm -rf /`）的关键安全屏障。
        - **执行 (Execute)**: 验证通过后，Go CLI 才会在本地环境中真正执行该命令。
    4.  **闭环反馈 (Closed-Loop Feedback)**: 命令执行的结果会再次被捕获，并作为新的输入提供给 LLM，形成“分析 -> 决策 -> 执行 -> 反馈”的智能诊断循环。

- **优势**: 该模式（也称为 **Function Calling** 或 **Tool Use**）是构建可靠、安全 Agent 的业界标准。它完美地结合了 LLM 的推理能力和本地代码的控制力。

### 5.4. 远程执行 (一)：抽象“执行器”模式 (SSH & Docker)

为了让工具能够诊断远程服务器和 Docker 环境，我们引入一个“执行器”抽象层，将“在哪里执行”与“执行什么”解耦。

- **方案核心：抽象执行器 (Executor Abstraction)**
    1.  **统一接口**: 在 Go 代码中定义一个 `Executor` 接口，它包含一个核心方法 `Execute(command string)`，负责返回命令的输出。
    2.  **多种实现**:
        -   **`LocalExecutor`**: 默认执行器，直接在本地操作系统上运行命令。
        -   **`SSHExecutor`**: 当用户通过 `--host user@hostname`、`--port 22`、`--key /path/to/key` 等参数指定了远程主机时，工具会初始化此执行器。它将使用 Go 的 SSH 库建立连接，并通过该连接在远程服务器上执行命令。
        -   **`DockerExecutor`**: 当用户提供了 `--docker-host` 参数时，会通过 Docker 的官方 Go SDK 连接到远程 Docker Daemon 执行相应操作。
    3.  **无缝集成**: 程序启动时根据用户参数选择并初始化一个“执行器”。后续的所有诊断逻辑（无论是“诊断套餐”还是“交互式深潜”）都通过这个执行器来执行命令，无需关心其具体实现。这使得核心逻辑保持简洁，同时轻松获得了强大的远程诊断能力。
    4.  **安全优先**: 优先使用 SSH 密钥和 Docker TLS 证书等行业标准方式进行安全认证，避免直接处理密码。

### 5.5. 远程执行 (二)：Kubernetes 的标准方式 (Kubeconfig)

与 Linux/Docker 不同，Kubernetes 的远程访问**不使用 SSH**。我们遵循其官方指定的、更安全和标准化的 `kubeconfig` 文件机制。

- **方案核心：拥抱 `kubeconfig`**
    1.  **`kubeconfig` 作为入口**: `kubeconfig` 文件包含了连接远程 K8s 集群所需的所有信息（API Server 地址、用户凭证、上下文）。这是与 K8s API 交互的唯一入口。
    2.  **提供 `--kubeconfig` 标志**: 所有 `k8s` 相关命令都会接受一个 `--kubeconfig` 标志，允许用户明确指定要使用的配置文件路径。
        -   **示例**: `./toolbox diagnose k8s --kubeconfig /path/to/remote.conf`
    3.  **遵循 `kubectl` 默认行为**: 如果用户未提供该标志，工具将自动遵循与 `kubectl` 完全相同的查找顺序：首先检查 `KUBECONFIG` 环境变量，然后查找默认路径 `~/.kube/config`。这保证了与用户现有环境的无缝兼容。
    4.  **实现方式**: Go 程序在执行 `kubectl` 命令时，只需将 `--kubeconfig` 参数传递给 `kubectl` 即可。`kubectl` 客户端自身会处理所有与远程 API 服务器的安全通信。如果使用 `client-go` 库，该库也原生支持从 `kubeconfig` 文件加载连接配置。

- **优势**: 此方法**标准化、安全、通用**，适用于任何云或自建的 K8s 集群，并能充分利用 K8s 内置的 RBAC 权限控制体系。
