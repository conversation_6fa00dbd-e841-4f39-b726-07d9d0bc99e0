# K8s/Linux/Nginx/Docker 智能排障工具箱设计方案

## 1. 项目愿景

创建一个统一的、自动化的命令行工具（CLI），旨在快速诊断和定位 Kubernetes、Linux 服务器、Nginx 和 Docker 环境中的常见问题。该工具通过预设的排查逻辑，引导用户或自动化脚本发现问题的根源，并提供修复建议，从而减少人工排查的时间和复杂性。

## 2. 核心设计

- **交互式与非交互式**：提供交互式模式，通过问答方式引导用户排查；同时提供非交互式模式，接受参数直接运行，方便集成到自动化脚本中。
- **模块化设计**：将 K8s、Linux、Nginx、Docker 的排查功能划分为独立的模块，方便维护和扩展。
- **安全性优先**：工具默认执行只读操作（如 `get`, `describe`, `logs`）。任何修改性操作（如删除 Pod、重启服务）都需要用户明确确认。
- **输出友好**：以清晰、易于理解的格式（如表格、高亮）展示诊断信息和结论。
- **可扩展性**：设计良好的插件机制，允许社区或团队贡献新的检查项和排查逻辑。

## 3. 技术选型

- **开发语言**：**Python** 或 **Go**。
    - **Python**：生态丰富，有大量用于系统管理和 API 交互的库（如 `kubernetes-py`, `docker-py`, `paramiko`），开发速度快。推荐使用 `Typer` 或 `Click` 构建 CLI。
    - **Go**：性能高，编译为静态二进制文件，分发方便，非常适合云原生工具开发。
- **本文档以 Python 为例进行规划。**

## 4. 功能模块与排查思路

### 模块一：Kubernetes 排查 (`./toolbox.py k8s`)

#### 4.1 Pod 状态异常
- **场景**：`CrashLoopBackOff`, `ImagePullBackOff`, `Pending`, `Error`
- **排查思路**：
    1.  **`check-pod <pod-name> -n <namespace>`**:
    2.  **获取 Pod 状态**：`kubectl get pod <pod-name> -o json`。
    3.  **分析状态**：
        - **`CrashLoopBackOff`**:
            - `kubectl describe pod <pod-name>`：检查 `Last State`，看退出原因 (Exit Code)。
            - `kubectl logs --previous <pod-name>`：获取上一次容器崩溃的日志，这是定位问题的关键。
            - `kubectl logs <pod-name>`：查看当前容器的日志。
            - **常见原因**：应用启动失败、配置错误、健康检查失败、内存溢出 (OOMKilled)。
        - **`ImagePullBackOff`**:
            - `kubectl describe pod <pod-name>`：查看 Events，找到拉取失败的镜像地址和错误信息。
            - **常见原因**：镜像名称或 Tag 错误、镜像仓库无法访问、`imagePullSecrets` 配置错误或缺失。
        - **`Pending`**:
            - `kubectl describe pod <pod-name>`：查看 Events，分析调度失败原因。
            - **常见原因**：节点资源不足 (CPU, Memory)、Taints/Tolerations 不匹配、Node Selector/Affinity 规则不满足。
        - **`Error`**:
            - `kubectl describe pod <pod-name>`：查看 Events，通常与存储卷 (PV/PVC) 挂载失败、配置字典 (ConfigMap) 或密钥 (Secret) 无法加载有关。

#### 4.2 Service/Ingress 访问异常
- **场景**：服务无法通过 ClusterIP、NodePort 或 Ingress 访问。
- **排查思路**：
    1.  **`check-svc <service-name>`**:
    2.  `kubectl get svc <service-name>`：检查 Service 的 `Selector` 是否正确。
    3.  `kubectl get pods -l <selector>`：根据 `Selector` 查看是否有匹配的、健康的 Pod。
    4.  `kubectl get endpoints <service-name>`：检查 Endpoints 是否有关联的 Pod IP。如果没有，说明 `Selector` 匹配不到 Pod。
    5.  **`check-ingress <ingress-name>`**:
    6.  `kubectl get ingress <ingress-name>`：检查 `Rules` 和 `Backend` 配置。
    7.  检查 Ingress Controller 的日志，查找与该 Ingress 相关的错误。

### 模块二：Linux 服务器排查 (`./toolbox.py linux`)

#### 4.1 系统资源占用高
- **场景**：CPU、内存、磁盘 I/O 或磁盘空间占用过高。
- **排查思路**：
    1.  **`check-resource`**:
    2.  **CPU**：`top -b -n 1 | head -n 15`，列出 CPU 占用最高的进程。
    3.  **内存**：`free -h` 和 `top -b -n 1 | head -n 15 | sort -k 10 -r`，列出内存占用最高的进程。
    4.  **磁盘空间**：`df -h`，检查各分区使用率。`du -sh /path/* | sort -rh | head -n 10`，找出占用空间最大的目录。
    5.  **磁盘 I/O**：`iostat -x 1 5`，查看 `%util` 和 `await`，定位高 I/O 的磁盘。`iotop -b -n 1`，找出高 I/O 的进程。

#### 4.2 网络连接问题
- **场景**：无法 `ping` 通目标地址、DNS 解析失败、端口不通。
- **排查思路**：
    1.  **`check-network --host <hostname> --port <port>`**:
    2.  **连通性**：`ping -c 4 <hostname>`。
    3.  **DNS 解析**：`nslookup <hostname>` 或 `dig <hostname>`。
    4.  **路由**：`traceroute <hostname>`。
    5.  **端口监听**：`ss -tunlp | grep <port>`，检查本地端口是否在监听。
    6.  **防火墙**：`iptables -L -n` 或 `firewall-cmd --list-all`，检查防火墙规则是否放行了端口。

### 模块三：Nginx 排查 (`./toolbox.py nginx`)

#### 4.1 配置检查
- **场景**：重载或启动 Nginx 失败。
- **排查思路**：
    1.  **`check-config`**:
    2.  直接运行 `nginx -t`，捕获输出，检查语法是否正确。

#### 4.2 访问日志分析
- **场景**：分析 5xx、4xx 错误码。
- **排查思路**：
    1.  **`analyze-log --path /var/log/nginx/access.log`**:
    2.  统计日志中 HTTP 状态码的分布 (`awk '{print $9}' access.log | sort | uniq -c`)。
    3.  列出出现次数最多的 Top 10 的 5xx/4xx 错误 URL。
    4.  检查 `error.log` 中与这些错误 URL 相关的 `upstream` 错误信息（如 "connect() failed", "upstream timed out"）。

### 模块四：Docker 排查 (`./toolbox.py docker`)

#### 4.1 容器状态异常
- **场景**：容器不断重启 (Restarting)、已退出 (Exited)。
- **排查思路**：
    1.  **`check-container <container_name_or_id>`**:
    2.  `docker ps -a --filter name=<name>`：查看容器状态。
    3.  `docker logs <container_id>`：查看容器日志，定位应用退出原因。
    4.  `docker inspect <container_id>`：检查 `State` 部分，查看 `ExitCode` 和 `Error` 信息。

#### 4.2 磁盘空间占用
- **场景**：Docker 占用的磁盘空间过大。
- **排查思路**：
    1.  **`check-disk-usage`**:
    2.  运行 `docker system df`，查看镜像、容器、本地卷、构建缓存分别占用的空间。
    3.  **提供清理建议**：
        - `docker system prune -a`：清理所有未使用的镜像、容器、网络和构建缓存。
        - `docker rmi $(docker images -f "dangling=true" -q)`：清理悬空的镜像。

## 5. 实施步骤

1.  **环境搭建**：初始化 Git 仓库，创建 Python 虚拟环境，安装 `typer`, `kubernetes`, `docker` 等基础库。
2.  **CLI 骨架**：使用 `Typer` 搭建主命令 `toolbox.py` 和 `k8s`, `linux`, `nginx`, `docker` 等子命令入口。
3.  **实现核心 Runner**：创建一个统一的函数，用于执行 shell 命令并捕获其 `stdout`, `stderr`, `exit_code`，方便后续调用。
4.  **开发 Linux 模块**：从最基础的 Linux 检查功能开始，因为它们是其他模块的基础。
5.  **开发 Docker 模块**：实现容器检查和磁盘清理功能。
6.  **开发 Nginx 模块**：实现配置检查和日志分析。
7.  **开发 K8s 模块**：这是最复杂的部分，需要调用 Kubernetes API，并根据不同的 Pod 状态实现详细的诊断逻辑。
8.  **编写单元测试和集成测试**：确保每个检查项都能正常工作。
9.  **文档和打包**：编写 `README.md`，说明如何安装和使用，并考虑使用 `PyInstaller` 等工具打包成独立的可执行文件。
