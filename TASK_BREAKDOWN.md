### 项目开发任务拆解 (Project Task Breakdown)

---

#### **里程碑 1: 核心 CLI 框架与本地诊断 (Core CLI Framework & Local Diagnosis)**

*目标：搭建一个可以运行的 Go CLI 程序，并实现对本地 Kubernetes 集群的“自主诊断”功能。*

*   **任务 1.1: 初始化项目结构**
    *   [x] 初始化 Go Module (`go mod init`)。
    *   [x] 引入 `Cobra` 库 (`go get github.com/spf13/cobra`) 用于构建 CLI。
    *   [x] 创建主命令 `toolbox` 和第一个子命令 `diagnose`。

*   **任务 1.2: 实现 `diagnose k8s` 命令**
    *   [x] 在 `diagnose` 下创建 `k8s` 子命令。
    *   [x] 硬编码 K8s 的“诊断套餐”命令列表（`kubectl get nodes`, `get pods`, etc.）。

*   **任务 1.3: 实现本地命令执行器**
    *   [x] 创建一个 `LocalExecutor`，负责在本地 shell 中执行命令。
    *   [x] 实现并发执行逻辑，让“诊断套餐”中的所有命令可以同时运行。
    *   [x] 编写逻辑来收集所有并发命令的 `stdout` 和 `stderr`，并整合成一个文本块。

---

#### **里程碑 2: LLM 智能汇总 (LLM-Powered Summary)**

*目标：将第一阶段收集到的数据发送给 LLM，并获取、展示结构化的健康摘要。*

*   **任务 2.1: 对接 LLM API**
    *   [ ] 创建一个 Go 客户端，用于与你选择的 LLM API (e.g., OpenAI, Gemini, Anthropic) 进行通信。
    *   [ ] 将 API Key 等敏感信息通过环境变量或配置文件进行管理。

*   **任务 2.2: 实现“智能汇总” Prompt**
    *   [ ] 在代码中构建在计划文档里设计的“你是一位顶级的 SRE 专家...”的 Prompt。
    *   [ ] 将**任务 1.3** 中收集到的数据作为上下文插入到 Prompt 中。

*   **任务 2.3: 解析并展示结果**
    *   [ ] 发送请求到 LLM API。
    *   [ ] 接收 LLM 返回的文本摘要。
    *   [ ] 对返回的 Markdown 文本进行格式化，并美观地打印在终端上。

---

#### **里程碑 3: 交互式深潜与对话记忆 (Interactive Deep-Dive & Memory)**

*目标：实现针对具体问题的多轮交互式诊断能力。*

*   **任务 3.1: 实现 `diagnose k8s-pod` 命令**
    *   [ ] 创建 `diagnose k8s-pod <pod-name>` 命令。
    *   [ ] 实现该命令的初始动作：执行 `kubectl describe pod <pod-name>`。

*   **任务 3.2: 实现“工具调用”循环**
    *   [ ] 设计并实现要求 LLM 返回 JSON 指令的 Prompt。
    *   [ ] 实现 JSON 解析逻辑，以提取 `tool_name` 和 `args`。
    *   [ ] 创建一个**命令白名单**，并实现安全检查逻辑。
    *   [ ] 实现一个循环，不断地“执行 -> 结果发给 LLM -> 获取新指令 -> 执行...”。

*   **任务 3.3: 实现对话记忆**
    *   [ ] 创建一个数据结构来存储对话历史。
    *   [ ] 在每次请求 LLM 时，将完整的历史记录包含进去。
    *   [ ] **(可选) ** 初步实现一个简单的“滑动窗口”策略来防止 Token 超限。

---

#### **里程碑 4: 远程执行能力 (Remote Execution)**

*目标：让工具具备诊断远程服务器和集群的能力。*

*   **任务 4.1: 抽象执行器接口**
    *   [ ] 定义 `Executor` 接口。
    *   [ ] 将**任务 1.3** 中的 `LocalExecutor` 重构为该接口的实现。

*   **任务 4.2: 实现 SSH 执行器**
    *   [ ] 引入 Go SSH 库。
    *   [ ] 创建 `SSHExecutor`，实现通过 SSH 连接远程服务器并执行命令。
    *   [ ] 为 `toolbox` 主命令添加 `--host`, `--port`, `--key` 等全局标志。

*   **任务 4.3: 实现 Kubeconfig 支持**
    *   [ ] 为所有 `k8s` 相关命令添加 `--kubeconfig` 标志。
    *   [ ] 修改 K8s 命令的构建逻辑，如果用户提供了该标志，则将其添加到 `kubectl` 命令中。
    *   [ ] 实现对 `KUBECONFIG` 环境变量和 `~/.kube/config` 默认路径的支持。

---

#### **里程碑 5: 扩展与完善 (Expansion & Polish)**

*目标：扩展工具的功能覆盖面，并提升其健壮性和用户体验。*

*   **任务 5.1: 扩展诊断目标**
    *   [ ] 添加 `diagnose linux` 命令和对应的“诊断套餐”。
    *   [ ] 添加 `diagnose docker` 命令和对应的“诊断套餐”。
    *   [ ] 添加更多细化的深潜命令，如 `diagnose k8s-node`, `diagnose docker-container`。

*   **任务 5.2: 提升健壮性**
    *   [ ] 添加单元测试和集成测试。
    *   [ ] 完善错误处理和日志记录。

*   **任务 5.3: 编写文档**
    *   [ ] 编写清晰的 `README.md`，包含安装、使用方法和示例。
    *   [ ] 为每个命令添加详细的帮助信息 (`--help`)。
