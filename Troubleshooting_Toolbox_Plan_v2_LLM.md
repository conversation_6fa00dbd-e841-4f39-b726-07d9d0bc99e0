# LLM 驱动的 K8s/Linux/Nginx/Docker 智能排障工具箱 v2.0

## 1. 项目愿景 (v2.0)

创建一个由大语言模型 (LLM) 驱动的智能排障工具。该工具能模拟领域专家的思维过程，通过多轮交互式诊断，动态地分析问题、提出假设、验证假设，并最终提供可执行的解决方案，从而实现对未知和复杂问题的自适应排查。

## 2. 核心架构：LLM 作为决策中心

我们摒弃了传统的静态脚本逻辑，采用“Go CLI + LLM”的动态循环架构。

- **Go CLI (执行器)**：作为与系统交互的“手和脚”。
    - 负责执行 LLM 下发的命令（如 `kubectl`, `docker`, `grep`）。
    - 负责调用云原生 API（Kubernetes API, Docker Engine API）。
    - 负责收集命令输出、日志、配置等上下文信息。
    - **它本身不包含复杂的排查逻辑。**

- **大语言模型 (LLM) (决策大脑)**：作为分析和决策的“大脑”。
    - 接收来自 Go CLI 的结构化上下文。
    - 分析上下文，推理问题根源。
    - **生成下一步需要执行的具体命令或操作指令。**
    - 在定位问题后，**生成具体的修复建议或可执行的修复命令**。

### 2.1 智能诊断循环 (The Loop)

![Workflow Diagram](https://i.imgur.com/J5aO6pS.png)  *（这是一个示意图，实际实现时为纯文本交互）*

1.  **启动**：用户通过 CLI 描述一个初始问题，例如：`./toolbox k8s check-pod my-pod-xyz -n default --reason "pod is in CrashLoopBackOff"`。
2.  **初步信息收集**：Go CLI 根据关键词（`k8s`, `check-pod`）执行一组预设的、最基础的诊断命令（例如 `kubectl describe pod my-pod-xyz`）。
3.  **请求 LLM 分析**：Go CLI 将用户问题和初步诊断的输出打包，发送给 LLM。Prompt 类似：“你是一个 K8s 专家。一个 Pod 处于 CrashLoopBackOff 状态，这是 describe 信息。请分析原因，并告诉我下一步应该执行什么命令来深入排查。请只返回命令本身。”
4.  **LLM 返回指令**：LLM 分析后，可能会返回一个新命令，例如 `kubectl logs --previous my-pod-xyz -n default`。
5.  **执行并反馈（循环）**：
    - Go CLI 向用户展示 LLM 建议的命令，并**请求确认**。
    - 用户确认后，Go CLI 执行该命令。
    - 将新命令的输出追加到上下文中，再次请求 LLM 分析（回到步骤 3）。
6.  **提出解决方案**：经过几轮循环，LLM 收集到足够信息，定位了问题（例如，日志显示数据库连接失败）。它将不再返回调查命令，而是返回一个解决方案，例如：
    > **诊断结论**: Pod 日志显示 'Connection refused' 错误，很可能是由于 ConfigMap 中的数据库地址 'db-host' 配置错误。
    > **建议操作**: 请检查名为 'app-config' 的 ConfigMap 中的 'DB_HOST' 键值。
    > **修复指令 (可选)**: 我可以为您生成一个 `kubectl patch` 命令来修正它，是否继续？
7.  **执行修复**：如果用户同意，Go CLI 将执行由 LLM 生成的修复命令。

## 3. 技术选型 (v2.0)

- **开发语言**: **Go**
    - **优势**: 性能高、静态编译、跨平台、强大的并发能力、云原生生态首选。
    - **核心库**: `Cobra` (构建强大的 CLI), `client-go` (与 K8s API 交互), `Docker SDK for Go`。
- **LLM API**: 需要配置一个大模型提供商的 API Key（例如 Google Gemini, OpenAI GPT等）。通过环境变量注入到工具中。

## 4. 安全性设计

- **用户确认**: 任何对系统产生修改的命令（`delete`, `patch`, `restart`, `rm` 等）或可能消耗大量资源的命令，都必须在执行前得到用户的明确输入（`y/n`）确认。
- **只读模式**: 可以提供一个 `--read-only` 标志，在此模式下，工具绝不会执行任何修改性操作。
- **命令审查**: 在执行前，清晰地向用户展示即将执行的完整命令。

## 5. 实施步骤 (v2.0)

1.  **环境初始化**：
    - `go mod init troubleshooting-toolbox`
    - 使用 `Cobra` 初始化 CLI 结构 (`./toolbox k8s`, `./toolbox linux` ...)。
2.  **开发“执行器”模块**：
    - 实现一个通用的函数 `executeCommand(command string)`，它能运行 shell 命令并安全地返回 `stdout`, `stderr`, `exit code`。
    - 封装 `client-go` 和 `docker-sdk` 的常用只读操作。
3.  **开发“LLM 通信”模块**：
    - 实现一个函数 `askLLM(context string)`，它负责读取环境变量中的 API Key，构建 Prompt，向 LLM 发送请求，并返回其响应。
4.  **实现主循环逻辑**：
    - 编写 `k8s check-pod` 命令的逻辑，实现上面描述的“智能诊断循环”。这是打通整个架构的关键。
5.  **逐步扩展**：
    - 在 `check-pod` 的成功基础上，将该模式应用到 `check-svc`, `linux check-cpu`, `nginx analyze-log` 等其他功能上。
    - 针对不同场景，优化初始收集信息的命令集和发送给 LLM 的基础 Prompt。
6.  **增强与打磨**：
    - 优化输出格式，对关键信息进行高亮。
    - 增加上下文管理能力，让 LLM 能“记住”之前的对话历史。
    - 编写完善的文档和使用示例。
